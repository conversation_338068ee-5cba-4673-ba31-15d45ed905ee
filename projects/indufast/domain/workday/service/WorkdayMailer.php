<?php

  namespace domain\workday\service;

  use classes\TimesTrait;

  class WorkdayMailer {

    use TimesTrait;

    public function send(): void {
      // Get all active employees
      $employees = \IndufastEmployee::find_all();
      $activeEmployees = \IndufastEmployee::filterActiveEmployees($employees);

      // Calculate date range for last week (Monday to Sunday)
      $endDate = date('Y-m-d', strtotime('last Sunday'));
      $startDate = date('Y-m-d', strtotime('last Monday', strtotime($endDate)));

      foreach ($activeEmployees as $employee) {
        if (empty($employee->email)) continue;

        $this->sendEmployeeWeeklyReport($employee, $startDate, $endDate);
      }
    }

    private function sendEmployeeWeeklyReport(\IndufastEmployee $employee, string $startDate, string $endDate): void {
      // Get workdays for the week
      $workdays = \IndufastWorkday::find_all("WHERE employee_id = {$employee->id} AND date >= '{$startDate}' AND date <= '{$endDate}' ORDER BY date ASC");
      if (!$workdays) return;

      // Calculate workdays and ensure we have data for each day
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      // Create workdays array indexed by date for easy lookup
      $workdaysByDate = [];
      foreach ($workdays as $workday) {
        $workdaysByDate[$workday->date] = $workday;
      }

      // Generate email content
      $emailContent = $this->generateEmailContent($employee, $startDate, $endDate, $workdaysByDate);

      // Send email using GsdMailer
      $subject = "Weekoverzicht werkuren - " . date('d-m-Y', strtotime($startDate)) . " t/m " . date('d-m-Y', strtotime($endDate));

      try {
        \GsdMailer::build($employee->email, $subject, $emailContent)->send();
      } catch (\Exception $e) {
        error_log("Failed to send workday email to {$employee->email}: " . $e->getMessage());
      }
    }

    private function generateEmailContent(\IndufastEmployee $employee, string $startDate, string $endDate, array $workdaysByDate): string {
      $html = '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .time { text-align: right; }
                .date { font-weight: bold; }
                .total-row { background-color: #f9f9f9; font-weight: bold; }
                .no-hours { color: #666; font-style: italic; }
            </style>
        </head>
        <body>';

      $html .= '<h2>Weekoverzicht werkuren</h2>';
      $html .= '<p><strong>Medewerker:</strong> ' . htmlspecialchars($employee->name) . '</p>';
      $html .= '<p><strong>Periode:</strong> ' . date('d-m-Y', strtotime($startDate)) . ' t/m ' . date('d-m-Y', strtotime($endDate)) . '</p>';

      $html .= '<table>';
      $html .= '<thead>';
      $html .= '<tr>';
      $html .= '<th>Datum</th>';
      $html .= '<th>Dag</th>';
      $html .= '<th>Werkuren</th>';
      $html .= '<th>Reisuren</th>';
      $html .= '<th>Verlof</th>';
      $html .= '<th>Bijzonder verlof</th>';
      $html .= '<th>Ziek</th>';
      $html .= '<th>Ongeoorloofd verzuim</th>';
      $html .= '</tr>';
      $html .= '</thead>';
      $html .= '<tbody>';

      // Initialize totals
      $totalWork = [];
      $totalTravel = [];
      $totalLeave = [];
      $totalSpecialLeave = [];
      $totalSick = [];
      $totalUnexcusedLeave = [];

      // Generate rows for each day of the week
      $currentDate = $startDate;
      while ($currentDate <= $endDate) {
        $dayName = $this->getDutchDayName($currentDate);
        $workday = $workdaysByDate[$currentDate] ?? null;

        $html .= '<tr>';
        $html .= '<td class="date">' . date('d-m-Y', strtotime($currentDate)) . '</td>';
        $html .= '<td>' . $dayName . '</td>';

        if ($workday) {
          // Work hours
          $workHours = $workday->workdayDurationNet ?: '00:00:00';
          $html .= '<td class="time">' . $this->formatTimeForDisplay($workHours) . '</td>';
          if ($workHours !== '00:00:00') $totalWork[] = $workHours;

          // Travel hours
          $travelHours = $workday->travelDurationNet ?: '00:00:00';
          $html .= '<td class="time">' . $this->formatTimeForDisplay($travelHours) . '</td>';
          if ($travelHours !== '00:00:00') $totalTravel[] = $travelHours;

          // Special hours
          $leave = $workday->specialHoursLeave ?: '00:00:00';
          $specialLeave = $workday->specialHoursSpecialLeave ?: '00:00:00';
          $sick = $workday->specialHoursSick ?: '00:00:00';
          $unexcusedLeave = $workday->specialHoursUnexcusedLeave ?: '00:00:00';

          $html .= '<td class="time">' . $this->formatTimeForDisplay($leave) . '</td>';
          $html .= '<td class="time">' . $this->formatTimeForDisplay($specialLeave) . '</td>';
          $html .= '<td class="time">' . $this->formatTimeForDisplay($sick) . '</td>';
          $html .= '<td class="time">' . $this->formatTimeForDisplay($unexcusedLeave) . '</td>';

          if ($leave !== '00:00:00') $totalLeave[] = $leave;
          if ($specialLeave !== '00:00:00') $totalSpecialLeave[] = $specialLeave;
          if ($sick !== '00:00:00') $totalSick[] = $sick;
          if ($unexcusedLeave !== '00:00:00') $totalUnexcusedLeave[] = $unexcusedLeave;
        } else {
          // No workday data
          $html .= '<td class="time no-hours">-</td>';
          $html .= '<td class="time no-hours">-</td>';
          $html .= '<td class="time no-hours">-</td>';
          $html .= '<td class="time no-hours">-</td>';
          $html .= '<td class="time no-hours">-</td>';
          $html .= '<td class="time no-hours">-</td>';
        }

        $html .= '</tr>';
        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
      }

      // Add totals row
      $html .= '<tr class="total-row">';
      $html .= '<td colspan="2"><strong>Totaal</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalWork)) . '</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalTravel)) . '</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalLeave)) . '</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalSpecialLeave)) . '</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalSick)) . '</strong></td>';
      $html .= '<td class="time"><strong>' . $this->formatTimeForDisplay($this->addTimes($totalUnexcusedLeave)) . '</strong></td>';
      $html .= '</tr>';

      $html .= '</tbody>';
      $html .= '</table>';
      $html .= '</body></html>';

      return $html;
    }

    private function getDutchDayName(string $date): string {
      $dayNames = [
        'Monday' => 'Maandag',
        'Tuesday' => 'Dinsdag',
        'Wednesday' => 'Woensdag',
        'Thursday' => 'Donderdag',
        'Friday' => 'Vrijdag',
        'Saturday' => 'Zaterdag',
        'Sunday' => 'Zondag'
      ];

      $englishDay = date('l', strtotime($date));
      return $dayNames[$englishDay] ?? $englishDay;
    }

    private function formatTimeForDisplay(string $time): string {
      if (empty($time) || $time === '00:00:00') {
        return '-';
      }

      // Convert HH:MM:SS to HH:MM for display
      return substr($time, 0, 5);
    }
  }