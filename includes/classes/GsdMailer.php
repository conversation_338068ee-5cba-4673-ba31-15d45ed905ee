<?php

  use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
  use Symfony\Component\Mailer\Mailer;
  use Symfony\Component\Mailer\Transport;
  use Symfony\Component\Mime\Address;
  use Symfony\Component\Mime\Crypto\DkimSigner;
  use Symfony\Component\Mime\Email;
  use Symfony\Component\Mime\Message;

  /**
   * Class GsdMailer
   * Standard class for sending emails.
   *
   * Short method:
   * GsdMailer::build($to, $subject, $message)->send();
   *
   * Extended method:
   * $gsdmailer = GsdMailer::build($to, $subject, $message);
   * $gsdmailer->addBcc("<EMAIL>");
   * $gsdmailer->addFile("pad naar bestand");
   * $gsdmailer->send();
   *
   * Send email function
   * Let op bij de $from parameter:
   * Dit moet altijd een emailadres zijn van onze klant, niet van een klant van de klant aangezien je dan een email stuurt met emailadres wat niet gekoppeld is aan de server.
   * Gebruik de ReplyTo functie om dit op te lossen, zodat de reply-to het emailadres van de klant van onze klant bevat.
   *
   * Toelichting SPF:
   * SPF valideert het emailadres in return-path ($bounceadres), niet het emailadres in de from header ($from).
   * De domeinnaam van $bounceadres moet dus ALTIJD matchen met SPF, en kan dus niet zomaar elk emailadres zijn.
   *
   * Toelichting DMARC:
   * DMARC bestaat uit DKIM en SPF, en nog extra policy's.
   * In geval van DMARC zouden de domeinnamen van from en return-path gelijk moeten zijn ($from=$bounceaddress)
   * https://postmarkapp.com/guides/dmarc
   * https://postmarkapp.com/blog/introducing-full-dmarc-support-with-custom
   *
   */
  class GsdMailer {

    const TRANSPORT_TYPE_SENDMAIL = "SENDMAIL";
    const TRANSPORT_TYPE_SMTP = "SMTP";

    private array $tos = []; //ontvanger(s)
    private array $froms = []; //afzenders(s) altijd mailadressen van klant volgens spf
    private array $ccs = []; //cc adressen
    private array $bccs = []; //bcc adressen
    private array $replyTos = []; //wanneer op beantwoorden word geklikt word dit emailadres getoond.
    private array $readReceiptTo = []; //lees bevestiging naar
    private string $returnPath = ""; //bounce emailadres
    private string $subject = ""; //onderwerp
    private string $message = ""; //bericht
    private array $files = []; //bestanden
    private bool $force = false; //if true, always send mail, also in development modus
    private $max_mail_size = 18.5; //the maximum size of a mail is oftenlimited to a maximum of 19mb (set to 18.5 to be sure)
    private bool $priority = false; //prioriteit
    private string $transportType = self::TRANSPORT_TYPE_SENDMAIL;
    private Message $email;

    public function __construct() {

      if (defined("MAIL_MAX_MAIL_SIZE")) { //set maximum mail size from config
        $this->setMaxMailSize(MAIL_MAX_MAIL_SIZE);
      }

      if (defined("MAIL_TRANSPORT_TYPE")) {
        $this->setTransportType(MAIL_TRANSPORT_TYPE);
      }
      elseif (ENVIRONMENT == 'LOCAL') {
        $this->setTransportType(self::TRANSPORT_TYPE_SMTP);
      }

    }

    /**
     * @return array
     */
    public function getTos(): array {
      return $this->tos;
    }

    /**
     * Set receiving addresses
     * @param array $tos
     */
    public function setTos(array $tos): void {
      $this->tos = $tos;
    }

    /**
     * Set receiving address(es)
     * @param string|array $to
     */
    public function setTo($to): void {
      $this->tos = [];
      if (is_array($to)) {
        foreach ($to as $key => $em) {
          $this->tos[$key] = StringHelper::convertEmailToIDN(trim($em));
        }
      }
      else {
        $this->tos[] = StringHelper::convertEmailToIDN(trim($to));
      }
    }

    /**
     * Add receiving addres
     * @param string $to
     */
    public function addTo(string $to): void {
      $this->tos[] = $to;
    }

    /**
     * Get sending addresses
     * @return array
     */
    public function getFroms(): array {
      return $this->froms;
    }

    /**
     * Set sending addresses
     * @param array $froms
     */
    public function setFroms(array $froms): void {
      $this->froms = $froms;
    }

    /**
     * Set sending address(es)
     * This can be mutiple addresses (strangly)
     * Possible from values: <EMAIL> / ['<EMAIL>', '<EMAIL>',...] / ['<EMAIL>'=>'display name', '<EMAIL>'=>'display name',...]
     * ALERT: this should always be a domainname our server is whitelisted to send email from,
     * else the e-mail wil be marked as spam.
     * @param string|array $from
     */
    public function setFrom($from): void {
      $this->froms = is_array($from) ? $from : [$from];
    }


    /**
     * Add sending addres
     * @param string $from
     */
    public function addFrom(string $from): void {
      $this->froms[] = $from;
    }


    /**
     * Get ccs addresses
     * @return array
     */
    public function getCcs(): array {
      return $this->ccs;
    }

    /**
     * Set ccs addresses
     * @param array $ccs
     */
    public function setCcs(array $ccs): void {
      $this->ccs = $ccs;
    }

    /**
     * Set cc address(es)
     * @param string|array $cc
     */
    public function setCc($cc): void {
      $this->ccs = [];
      if (is_array($cc)) {
        foreach ($cc as $key => $em) {
          if ($em == "") continue;
          $this->ccs[$key] = StringHelper::convertEmailToIDN(trim($em));
        }
      }
      else {
        if ($cc == "") return;
        $this->ccs[] = StringHelper::convertEmailToIDN(trim($cc));
      }
    }

    /**
     * Add cc addres
     * @param string $cc
     */
    public function addCc(string $cc): void {
      if ($cc == "") return;
      $this->ccs[] = $cc;
    }

    /**
     * Get bcc addresses
     * @return array
     */
    public function getBccs(): array {
      return $this->bccs;
    }

    /**
     * Set bcc addresses
     * @param array $bccs
     */
    public function setBccs(array $bccs): void {
      if ($this->getTransportType() == self::TRANSPORT_TYPE_SMTP && !DEVELOPMENT) {
        throw new GsdException("Het is niet mogelijk om bcc te gebruiken bij een SMTP mailer. Verstuur de email als 2 losse e-mails.");
      }
      $this->bccs = $bccs;
    }

    /**
     * Set bcc address(es)
     * @param string|array $bcc
     */
    public function setBcc($bcc): void {
      if ($this->getTransportType() == self::TRANSPORT_TYPE_SMTP && !DEVELOPMENT) {
        throw new GsdException("Het is niet mogelijk om bcc te gebruiken bij een SMTP mailer. Verstuur de email als 2 losse e-mails.");
      }
      if (empty($bcc)) return;
      $this->bccs = [];
      if (is_array($bcc)) {
        foreach ($bcc as $key => $em) {
          if (empty($em)) continue;
          $this->bccs[$key] = StringHelper::convertEmailToIDN(trim($em));
        }
      }
      else {
        $this->bccs[] = StringHelper::convertEmailToIDN(trim($bcc));
      }
    }


    /**
     * Add bcc addres
     * @param string $bcc
     */
    public function addBcc(string $bcc): void {
      if ($this->getTransportType() == self::TRANSPORT_TYPE_SMTP && !DEVELOPMENT) {
        throw new GsdException("Het is niet mogelijk om bcc te gebruiken bij een SMTP mailer. Verstuur de email als 2 losse e-mails.");
      }
      if (empty($bcc)) return;
      $this->bccs[] = $bcc;
    }

    /**
     * Set subject
     * @return string
     */
    public function getSubject(): string {
      return $this->subject;
    }

    /**
     * Set subject
     * @param string $subject
     */
    public function setSubject(string $subject): void {
      $this->subject = $subject;
    }

    /**
     * Get html message
     * @return string
     */
    public function getMessage(): string {
      return $this->message;
    }

    /**
     * Set html message
     * @param string $message
     */
    public function setMessage(string $message): void {
      $this->message = $message;
    }

    /**
     * Get filepath array
     * @return array
     */
    public function getFiles(): array {
      return $this->files;
    }

    /**
     * Set files array
     * @param array $files : if the array key contains a string, this is used as the filename
     */
    public function setFiles(array $files): void {
      $this->files = $files;
    }

    /**
     * Add file
     * @param string $filepath : full path to file, including filename
     * @param bool $filename : optional name to use for the file, if empty the filename is used from the filepath
     */
    public function addFile(string $filepath, $filename = false): void {
      if ($filename !== false && !is_numeric($filename)) {
        $this->files[$filename] = $filepath;
      }
      else {
        $this->files[] = $filepath;
      }
    }

    /**
     * Get reply to addresses (click on answer in your mailclient)
     * @return array
     */
    public function getReplyTos(): array {
      return $this->replyTos;
    }

    /**
     * Set reply to addresses (click on answer in your mailclient)
     * @param array $replyTos
     */
    public function setReplyTos(array $replyTos): void {
      $this->replyTos = $replyTos;
    }

    /**
     * Set reply to address(es) (click on answer in your mailclient)
     * @param string|array $replyTo
     */
    public function setReplyTo($replyTo): void {
      $this->replyTos = is_array($replyTo) ? $replyTo : [$replyTo];
    }


    /**
     * Get return address or bounce address
     * @return string
     */
    public function getReturnPath(): string {
      return $this->returnPath;
    }

    /**
     * Set return address or bounce address
     * @param string $returnPath
     */
    public function setReturnPath(string $returnPath): void {
      $this->returnPath = $returnPath;
    }

    /**
     * Set readreciept addreses
     * @return array
     */
    public function getReadReceiptTo(): array {
      return $this->readReceiptTo;
    }

    /**
     * Set readreciept address(es)
     * @param string|array $readReceiptTo
     */
    public function setReadReceiptTo($readReceiptTo): void {
      $this->readReceiptTo = is_array($readReceiptTo) ? $readReceiptTo : [$readReceiptTo];
    }

    /**
     * Is forcly send (also in development modus)
     * @return bool
     */
    public function isForce(): bool {
      return $this->force;
    }

    /**
     * Set force send
     * @param bool $force
     */
    public function setForce(bool $force): void {
      $this->force = $force;
    }

    /**
     * @return Message
     */
    public function getEmail() {
      return $this->email;
    }

    /**
     * @param Message $email
     */
    public function setEmail(Message $email): void {
      $this->email = $email;
    }

    /**
     * Get the maximum mail size limit in Mb.
     * If the file size total exceeds max_mail_size an error is thrown
     * On most mailservers this value is 19 Mb. It's posible to set this with config MAIL_MAX_MAIL_SIZE
     *
     * @return int
     */
    public function getMaxMailSize() {
      return $this->max_mail_size;
    }

    /**
     * Set the maximum mail size limit in Mb.
     * @param int $max_mail_size
     */
    public function setMaxMailSize($max_mail_size) {
      $this->max_mail_size = $max_mail_size;
    }

    /**
     * Is this email high priority?
     * @return bool
     */
    public function isPriority(): bool {
      return $this->priority;
    }

    /**
     * Set the email as high priority
     * @param bool $priority
     */
    public function setPriority(bool $priority): void {
      $this->priority = $priority;
    }

    /**
     * Set transport type SMPT / SENDMAIL
     * @return string
     */
    public function getTransportType(): string {
      return $this->transportType;
    }

    /**
     * Get transporttype
     * @param string $transportType
     */
    public function setTransportType(string $transportType): void {
      $this->transportType = $transportType;
    }


    /**
     * Build the GSD mailer class
     * @param string|array $to
     * @param string $subject
     * @param string $message
     * @return GsdMailer
     */
    public static function build($to = "", string $subject = "", string $message = ""): GsdMailer {
      $gsdmailer = new GsdMailer();
      $gsdmailer->setTo($to);
      $gsdmailer->setSubject($subject);
      $gsdmailer->setMessage($message);
      return $gsdmailer;
    }

    /**
     * Send message
     * @return bool: true on succes, false on failure
     */
    public function send(): bool {

      //defaults
      if (count($this->getFroms()) == 0) $this->setFrom(MAIL_FROM);
      if ($this->getReturnPath() == "") $this->setReturnPath(MAIL_BOUNCE_ADDRESS);
      if (count($this->getReplyTos()) == 0) $this->setReplyTos($this->getFroms());

      $this->developmentmodus();
      $this->errorEmail();

      $mailer = $this->buildMailer();

      $mes = new Email();
      $mes->getHeaders()->addTextHeader('X-Mailer', 'PHP v' . phpversion());

      $mes->subject($this->getSubject());

      $mes->from(...GsdMailer::arrayToAddresses($this->getFroms()));
      $mes->sender(...GsdMailer::arrayToAddresses($this->getFroms())); //dit mag ook een array zijn
      $mes->to(...GsdMailer::arrayToAddresses($this->getTos()));

      if (count($this->getReplyTos()) > 0) $mes->replyTo(...GsdMailer::arrayToAddresses($this->getReplyTos()));
      if ($this->getReturnPath() != "") $mes->returnPath($this->getReturnPath());
      if (count($this->getReadReceiptTo()) > 0) {
        $mes->getHeaders()->addTextHeader('X-Confirm-Reading-To', implode(",", $this->getReadReceiptTo()));
      }


      if (count($this->getBccs()) > 0) $mes->bcc(...GsdMailer::arrayToAddresses($this->getBccs()));
      if (count($this->getCcs()) > 0) $mes->cc(...GsdMailer::arrayToAddresses($this->getCcs()));

      $mes->html($this->getMessage());

      //cleanup html
      $plainText = $this->getMessage();
      preg_match("/<body[^>]*>(.*?)<\/body>/is", $plainText, $matches);
      if (count($matches) == 2 && !empty($matches[1])) { //body tag found, set content
        $plainText = $matches[1];
      }
      $plainText = trim(strip_tags(\StringHelper::br2nl(str_replace(["\r\n", "\n"], "", $plainText))));
      if (!empty($plainText)) {
        $mes->text($plainText);//add text variant. Less chance on spam
      }

      if ($this->isPriority()) {
        $mes->priority(Email::PRIORITY_HIGHEST);
      }

      $files_total_size = 0;
      foreach ($this->getFiles() as $filekey => $filepath) {
        if (file_exists($filepath)) {
          if (is_string($filekey)) {
            $mes->attachFromPath($filepath, $filekey);
          }
          else {
            $mes->attachFromPath($filepath);
          }
          $files_total_size += filesize($filepath);
        }
        else {
          logToFile("mailerrors", 'GsdMailer probeert een bestand toevoegen wat niet bestaat? Filepath: ' . $filepath);
          throw new GsdException('GsdMailer probeert een bestand toevoegen wat niet bestaat? Filepath: ' . $filepath . "\nE-mail onderwerp: '" . $this->getSubject() . "'\n" . $this->getMessage(), E_USER_ERROR);
        }
      }

      if (FileHelper::convertBytesToMb($files_total_size) > $this->getMaxMailSize()) {
        logToFile("mailerrors", "Mail error: " . "Mail niet verzonden: bestandsformaat van deze email is groter dan " . $this->getMaxMailSize() . " Mb. Huidig formaat: " . FileHelper::convertBytesToMb($files_total_size) . " Mb. Verwijder een aantal bijlagen uit uw email. E-mail onderwerp: '" . $this->getSubject() . "'\n" . $this->getMessage());
        throw new GsdException("GsdMailer: mail niet verzonden. Bestandsformaat van e-mail is groter dan " . $this->getMaxMailSize() . " Mb. Huidig formaat: " . FileHelper::convertBytesToMb($files_total_size) . " Mb. Verwijder een aantal bijlagen uit uw email. E-mail onderwerp: '" . $this->getSubject() . "'", E_USER_ERROR);
      }

      $dkim_signer = $this->dkim();
      if ($dkim_signer) {
        $mes = $dkim_signer->sign($mes);
      }

      try {

        GsdMailerSleeper::checkSleep();

        $mailer->send($mes);
        $this->logSuccess(true);
        $this->setEmail($mes);

        GsdMailerSleeper::registerSend();

        return true;
      }
      catch (TransportExceptionInterface $e) {
        $this->logSuccess(false);
        $this->logError($e->getMessage());
      }

      return false;
    }

    /**
     * @param array $emails
     * @return Address[]
     */
    public static function arrayToAddresses($emails) {
      $addresses = [];
      foreach ($emails as $email => $desc) {
        if (is_string($email)) { //andere naam gedefineerd
          $addresses[] = new Address($email, $desc);
        }
        else {
          $addresses[] = new Address($desc);
        }
      }
      return $addresses;
    }

    /**
     * Log succesfully send messages
     * @param bool $success
     */
    private function logSuccess($success): void {

      if ($success && defined("LOG_MAILS_ALL") && LOG_MAILS_ALL) {
        global $current_action;
        $logmsg = "Mail send to [" . implode(',', $this->getTos()) . "]";
        $logmsg .= " subject [" . $this->getSubject() . "]";
        if (isset($current_action) && is_object($current_action) && is_a($current_action, "gsdActions")) {
          $logmsg .= " called in [" . $current_action->toString() . "]";
        }
        logToFile("mails", $logmsg);
      }

      if (Config::isdefined('LOG_ALL_MAIL_TO_DB') && Config::get('LOG_ALL_MAIL_TO_DB') && !in_array(MAIL_ERRORS, $this->getTos())) {
        // log all send emails to the database. Error emails niet loggen in db, die willen we niet aan de klant laten zien.
        $mail_log = new MailLog();
        if (strlen(implode(',', $this->getTos())) >= 255) {
          $mail_log->email_to = substr(implode(',', $this->getTos()), 0, 252) . '...';
        }
        else {
          $mail_log->email_to = implode(',', $this->getTos());
        }
        $mail_log->email_from = implode(',', $this->getFroms());
        $mail_log->subject = $this->getSubject();
        $ccs = $this->getCcs();
        if (ArrayHelper::hasData($ccs)) {
          $mail_log->email_cc = implode(',', $ccs);
        }
        $bccs = $this->getBccs();
        if (ArrayHelper::hasData($bccs)) {
          $mail_log->email_bcc = implode(',', $bccs);
        }
        if (!$success) {
          $mail_log->status = 'failed';
        }
        else {
          $mail_log->status = 'success';
        }
        $mail_log->insertTS = date('Y-m-d H:i:s');

        $mail_log->save();
      }
    }


    /**
     * Log failure and trigger error
     * @param $errormessage
     */
    private function logError($errormessage): void {
      $logmes = "Mail error: " . $errormessage . "\n";
      $logmes .= "Subject: " . $this->getSubject() . "\n";
      $logmes .= "Message: " . $this->getMessage() . "\n";
      $logmes .= "Tos: " . implode(",", $this->getTos()) . "\n";
      if (count($this->getFiles()) > 0) {
        $logmes .= "Files:\n";
        $filesizetotal = 0;
        foreach ($this->getFiles() as $file) {
          $filesizestr = "?";
          if (file_exists($file)) {
            $filesize = filesize($file);
            $filesizetotal += $filesize;
            $filesizestr = FileHelper::convertBytesToMb($filesize);
          }
          $logmes .= $file . ", filesize: " . $filesizestr . " Mb\n";
        }
        $logmes .= "Filesize total: " . FileHelper::convertBytesToMb($filesizetotal) . " Mb\n";
      }
      logToFile("mailerrors", $logmes);
      throw new GsdException("GsdMailer: fout bij verzenden e-mail: " . $errormessage . ". Onderwerp: " . $this->getSubject(), E_USER_ERROR);
    }

    /**
     * If DKIM is configured, attach the dkim private key to the message
     * make sure the DKIM is also set on the domain DNS
     * DKIM is domain specific. So check domain in from emailadres, or you can use * as wildcard config to use for all domainnames*
     * @return bool|DkimSigner
     */
    private function dkim() {
      if (Config::isdefined("DKIM")) {
        $cemail = array_values($this->getFroms())[0];
        $domain = substr($cemail, strrpos($cemail, '@') + 1);
        $dkim_config = Config::get("DKIM");
        if (!isset($dkim_config[$domain])) {
          //this domain has no dkim config, fallback wildcard config if set
          $domain = '*';
        }
        if (isset($dkim_config[$domain])) {
          $dkim_signer = new DkimSigner(
            $dkim_config[$domain]['DKIM_PRIVATE_KEY'],
            $dkim_config[$domain]['DKIM_DOMAIN'],
            $dkim_config[$domain]['DKIM_SELECTOR'],
            [],
            $dkim_config[$domain]['DKIM_PASSWORD'] ?? '',
          );
          return $dkim_signer;
        }
      }
      return false;
    }

    /**
     * LOCAL, isForce(): always send
     * PRODUCTION AND DEVELOPMENT=false: send
     * STAGING AND DEVELOPMENT=false: send
     * User::SUPERADMIN_EMAILS emailadresses (ex <EMAIL>) are always send! Handy if you use password forgotten on staging
     */
    private function developmentmodus(): void {

      //verzenden op PRODUCTION als DEVELOPMENT false
      if(ENVIRONMENT == 'PRODUCTION' && (!defined('DEVELOPMENT') || DEVELOPMENT == false)) {
        return;
      }

      //verzenden op STAGING als DEVELOPMENT false
      if(ENVIRONMENT == 'STAGING' && (!defined('DEVELOPMENT') || DEVELOPMENT == false)) {
        //wel even in het subject markeren dat het om staging gaat.
        $this->setSubject("TEST " . ENVIRONMENT . ": " . $this->getSubject());
        return;
      }

      //altijd verzenden op LOCAL || isForce()
      if(ENVIRONMENT == 'LOCAL' || $this->isForce()) {
        return;
      }

      //hier, dan afvangen...

      $this->setSubject("TEST " . ENVIRONMENT . ": " . $this->getSubject());
      $this->setCcs([]);
      $this->setBccs([]);

      $error_email = MAIL_ERRORS;
      if (is_array(MAIL_ERRORS)) {
        //pak even de eerste error email
        $error_email = MAIL_ERRORS[0];
      }
      if (in_array($error_email, $this->getTos())) {
        //LOCAL, altijd verzenden developer emailadres
        //STAGING, altijd verzenden developer emailadres
        $this->setFrom($error_email);
        $this->setTo(MAIL_ERRORS);
        $this->setReturnPath($error_email);
        $this->setReplyTo(MAIL_ERRORS);
        $this->setReadReceiptTo([]);
      }
      else {
        //DEVELOPMENT = true, we zitten op STAGING of PRODUCTION
        //DEVELOPMENT zou eigenlijk niet aan moeten staan op PRODUCTION, maar dat zou kunnen bij een nieuw project
        //als DEVELOPMENT=true en er wordt een gedefineerd @gsd.nl emailadres gebruikt, dan verzenden naar dat emailadres.
        $to = MAIL_DEVELOPMENT;
        foreach ($this->getTos() as $name => $email) {
          if (isset(User::SUPERADMIN_EMAILS[$email])) {
            //sa emailadres, mappen naar medewerker emailadres
            $to = User::SUPERADMIN_EMAILS[$email];
            break;
          }
          elseif (in_array($email, User::SUPERADMIN_EMAILS)) {
            //gsd medewerker emailadres
            $to = $email;
            break;
          }
        }

        if ($to == MAIL_DEVELOPMENT) {
          //verzenden naar MAIL_DEVELOPMENT, dit kan zijn gsd of klant op een staging bijv.
          $this->setTo(MAIL_DEVELOPMENT);
          $this->setFrom(MAIL_DEVELOPMENT);
          $this->setReturnPath(MAIL_DEVELOPMENT);
          $this->setReplyTo(MAIL_DEVELOPMENT);
          if (count($this->getReadReceiptTo()) > 0) {
            $this->setReadReceiptTo([MAIL_DEVELOPMENT]);
          }
        }
        else {
          //dit was een SUPERADMIN emailadres, verzenden naar deze developer, ook al is DEVELOPMENT=true
          $this->setTo($to);
          if (count($this->getReadReceiptTo()) > 0) {
            $this->setReadReceiptTo([]);
          }
        }
      }

    }

    /**
     * Check if email is error email. If so, set returnpath to prevent bounce to customer emailaddress
     * Why?
     * The situation may acccore that outgaing mail has a rate-limit.
     * During release/error a lot of error mails may be sent tot MAIL_ERRORS. When the rate-limit is triggered
     * these emails will be baunced back to the return-path, standard MAIL_FROM. To prevent these emails to
     * go to the customer, we bounce them to the blackhole (sun).
     *
     */
    private function errorEmail(): void {
      //if not in development modus, and has error emailadres in tos
      if (defined('DEVELOPMENT') && !DEVELOPMENT) {
        if (is_string(MAIL_ERRORS) && in_array(MAIL_ERRORS, $this->getTos())) {
          $this->setReturnPath("<EMAIL>"); //for now <NAME_EMAIL>
        }
        elseif (is_array(MAIL_ERRORS) && in_array(MAIL_ERRORS[0], $this->getTos())) {
          $this->setReturnPath("<EMAIL>"); //for now <NAME_EMAIL>
        }
      }
    }

    /**
     * Build mailer
     * @return Mailer
     */
    private function buildMailer(): Mailer {

      $dsn = "";
      if ($this->getTransportType() == self::TRANSPORT_TYPE_SENDMAIL) {
        if (defined("MAIL_TRANSPORT_COMMAND")) {
          $dsn = 'sendmail://default?command=' . rawurlencode(MAIL_TRANSPORT_COMMAND);
        }
        else {
          $dsn = 'sendmail://default';
        }

        //for sending via sendmail in swiftmailer, proc_open should be available. extra check if this is enabled
        if (!function_exists("proc_open")) {
          mail("<EMAIL>", "PROC_OPEN NIET BESCHIKBAAR! MAIL NIET VERZONDEN", "PROC_OPEN NIET BESCHIKBAAR! Dit is verplicht bij het verzenden via sendmail met swiftmailer: " . PROJECT . " - " . $_SERVER["DOCUMENT_ROOT"] . "\nNeem contact op met Cyberfusion, er is waarschijnlijk een server update geweest.");
        }

      }
      elseif ($this->getTransportType() == self::TRANSPORT_TYPE_SMTP) {
        $dsn = "smtp://" . MAIL_SMTP_USERNAME . ":" . MAIL_SMTP_PASSWORD . "@" . MAIL_SMTP_SERVER . ":" . MAIL_SMTP_SERVER_PORT . "?encryption=" . MAIL_SMTP_SERVER_SECURITY . "&verify_peer=0";
      }
      else {
        mail("<EMAIL>", "ONBEKEND TRANSPORT MAIL TYPE", "ONBEKEND TRANSPORT MAIL TYPE");
        ResponseHelper::redirectError("Undefined mail protocol");
      }

      $transport = Transport::fromDsn($dsn);

//      @todo swiftmailer rfc 6530 is not yet working for internationalized emailadresses aka bjö*********.
//      The following code should solve this, but it didn't work. (tested local, maybe works on production server)
//      $smtpUtf8 = new Swift_Transport_Esmtp_SmtpUtf8Handler();
//      $transport->setExtensionHandlers([$smtpUtf8]);
//      $utf8Encoder = new Swift_AddressEncoder_Utf8AddressEncoder();
//      $transport->setAddressEncoder($utf8Encoder);

      $mailer = new Mailer($transport);


      return $mailer;
    }

  }

  /**
   * Limit the number of mails send per period. (ratelimit)
   * Cyberfusion ratelimit:
   * - 20 e-mails per seconde vanaf hetzelfde ip-adres naar ieder e-mailadres
   * - 2.5 e-mails per seconde naar hetzelfde e-mailadres vanaf ieder e-mailadres (dit is niet afgevangen)
   * Staat nu 9 mails per seconde, zodat we goed zitten. Er zouden dus 2 appicaties vanaf de server zonder problemen een mailing moeten kunnen sturen.
   * 25-05-2023 - Robert: microsoft heeft snel de neiging om iets op de blocklist te zetten.
   * Standaard staat daarom de rate van emails vanuit de cron op 2 emails per seconden.
   */
  class GsdMailerSleeper {

    static $instance = null;
    private $rate = 9; //number of emails per second. Default is 9 per second
    private int $send = 0;
    private int $starttime; //time when mailing started

    public function __construct() {
      $this->starttime = time();
    }

    /**
     * Number of emails send per second
     * @return int
     */
    public function getRate(): int {
      return $this->rate;
    }

    /**
     * Set number of emails per second
     * @param int $rate
     */
    public function setRate(int $rate): void {
      $this->rate = $rate;
    }


    /**
     * Get number of send emails in this php script
     * @return int
     */
    public function getSend(): int {
      return $this->send;
    }

    /**
     * Set number of send emails in this php script
     * @param int $send
     */
    public function setSend(int $send): void {
      $this->send = $send;
    }

    /**
     * Add 1 email send
     * @return void
     */
    public function add(): void {
      $this->send++;
    }

    public static function getInstance(): ?GsdMailerSleeper {
      if (is_null(self::$instance)) {
        self::$instance = new GsdMailerSleeper();

        //mails vanuit de cron zijn gemaximaliseerd op 2 per seconden, zodat blacklist kans beperkt word
        if (isset($_SESSION["cron"])) {
          self::$instance->setRate(2);
        }

      }
      return self::$instance;
    }

    /**
     * Register a mail send
     * @return void
     */
    public static function registerSend(): void {
      $instance = self::getInstance();
      $instance->add();
    }

    /**
     * Check if mailer should sleep
     * @return void
     */
    public static function checkSleep(): void {
      $instance = self::getInstance();
      $instance->sleep();
    }

    /**
     * Sleep sending if more than rate emails are send within 1 second
     * @return bool true if slept
     */
    public function sleep(): bool {
//      pd("Aantal emails verzonden in dit script: ".$this->send);
      if ($this->send == 0) return false; //no sleep when first mail is send

      if ($this->send >= 20 && $this->getRate() > 2) {
        //when more than 20 emails are send, start slowing down to prevent mailserver block
        //2 mails per second instead of default rate (probably 9)
        $this->setRate(2);
      }

      $expectedDuration = $this->send / $this->rate; //tijd welke verlopen zou moeten zijn bij dit aantal verzonden emails
      $timePassed = (time() - $this->starttime); //tijd verlopen in seconden sinds start mailen
//      pd("Toegestande verzend duur: ".$expectedDuration . ' - verlopen tijd in seconden' . $timePassed);
      $sleep = ceil($expectedDuration - $timePassed); //aantal extra seconden nodig, om de mailserver niet over de flos te laten gaan.
      if ($sleep > 0) {
//        echo "Slapen aantal seconden: ".$sleep;
        sleep($sleep);
        return true;
      }
      return false;

    }

  }
